<?php

namespace App\Http\Controllers;

use App\Services\DonorService;
use Illuminate\Http\Request;

class DonorController extends Controller
{


    protected $donorService;

    public function __construct(DonorService $donorService)
    {
        $this->donorService = $donorService;
    }

    public function detailView(Request $request)
    {



        $data = $this->donorService->getDetailViewData($request);
        return view('donor.detail', $data);
    }
}
