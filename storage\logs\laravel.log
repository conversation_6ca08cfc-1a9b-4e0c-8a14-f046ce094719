
[2025-07-14 12:53:54] local.DEBUG: ✅ Inside staging/local environment  
[2025-07-14 12:53:54] local.DEBUG: 🍪 staging_access cookie: NULL  
[2025-07-14 12:53:54] local.DEBUG: ✅ Valid cookie present. Access granted  
[2025-07-14 12:55:55] staging.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-14 12:56:00] local.DEBUG: ✅ Inside staging/local environment  
[2025-07-14 12:56:00] local.DEBUG: token:   
[2025-07-14 12:56:00] local.DEBUG: 🍪 staging_access cookie: NULL  
[2025-07-14 12:56:00] local.DEBUG: ✅ Valid cookie present. Access granted  
[2025-07-14 12:57:16] staging.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-14 12:57:30] local.DEBUG: ✅ Inside staging/local environment  
[2025-07-14 12:57:30] local.DEBUG: token: gauntlet  
[2025-07-14 12:57:30] local.DEBUG: 🍪 staging_access cookie: NULL  
[2025-07-14 12:57:30] local.DEBUG: ❌ No valid cookie set  
[2025-07-14 12:57:30] local.DEBUG: 🚫 Redirecting to live site  
[2025-07-14 12:58:33] staging.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-14 12:58:47] local.DEBUG: ✅ Inside staging/local environment  
[2025-07-14 12:58:47] local.DEBUG: token: gauntlet  
[2025-07-14 12:58:47] local.DEBUG: 🍪 staging_access cookie: NULL  
[2025-07-14 12:58:47] local.DEBUG: ❌ No valid cookie set  
[2025-07-14 12:58:47] local.DEBUG: 🚫 Redirecting to live site  
[2025-07-14 12:59:08] staging.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-14 13:07:20] staging.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 06:24:57] staging.DEBUG: Inside staging environment  
[2025-07-15 06:24:57] staging.DEBUG: token: gauntlet  
[2025-07-15 06:24:57] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:24:57] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:24:57] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:05] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:05] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:05] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:05] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:05] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:06] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:06] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:06] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:06] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:06] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:15] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:15] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:15] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:15] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:15] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:19] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:19] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:19] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:19] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:19] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:44] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:44] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:44] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:44] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:44] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:46] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:46] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:46] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:46] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:46] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:52] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:52] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:52] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:52] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:52] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:52] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:52] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:52] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:52] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:52] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:26:18] staging.DEBUG: Inside staging environment  
[2025-07-15 06:26:18] staging.DEBUG: token: gauntlet  
[2025-07-15 06:26:18] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:26:18] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:26:18] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:26:19] staging.DEBUG: Inside staging environment  
[2025-07-15 06:26:19] staging.DEBUG: token: gauntlet  
[2025-07-15 06:26:19] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:26:19] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:26:19] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:26:45] staging.DEBUG: Inside staging environment  
[2025-07-15 06:26:45] staging.DEBUG: token: gauntlet  
[2025-07-15 06:26:45] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:26:45] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:26:45] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:26:48] staging.DEBUG: Inside staging environment  
[2025-07-15 06:26:48] staging.DEBUG: token: gauntlet  
[2025-07-15 06:26:48] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:26:48] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:26:48] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:26:59] staging.DEBUG: Inside staging environment  
[2025-07-15 06:26:59] staging.DEBUG: token: gauntlet  
[2025-07-15 06:26:59] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:26:59] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:26:59] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:27:04] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:04] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:04] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:27:04] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:27:04] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:27:10] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:10] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:10] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:27:10] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:27:10] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:27:27] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:27] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:27] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:27:27] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:27:27] staging.DEBUG:  Access token matched. Setting cookie.  
[2025-07-15 06:27:29] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:29] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:29] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:27:29] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:27:35] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:35] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:35] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:27:35] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:27:37] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:37] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:37] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:27:37] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:27:46] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:46] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:46] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:27:46] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:29:16] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2MZKQoAc42Xce0lpMYfce","idempotency_key":"payment_intent_c0e0690c633e28421d63abd8e90acdc9","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:29:16] staging.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl2MZKQoAc42Xce0lpMYfce","error_code":"incomplete_number","error_message":"Your card number is incomplete.","donation_type":"user_donation"} 
[2025-07-15 06:35:00] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2S8KQoAc42Xce0fN1xv3I","idempotency_key":"payment_intent_005d66a26b5732d43fffeb546ed713fd","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:35:07] staging.DEBUG: Inside staging environment  
[2025-07-15 06:35:07] staging.DEBUG: token: gauntlet  
[2025-07-15 06:35:07] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:35:07] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:35:36] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2ShKQoAc42Xce1VHBeXrj","idempotency_key":"payment_intent_cab25a310e8c950d65480d831026af57","email":"<EMAIL>","amount":"250"} 
[2025-07-15 06:37:05] staging.DEBUG: Inside staging environment  
[2025-07-15 06:37:05] staging.DEBUG: token: gauntlet  
[2025-07-15 06:37:05] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:37:05] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:37:38] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2UgKQoAc42Xce1Od6NoLP","idempotency_key":"payment_intent_10d3b8a2aa9e7b723c364d91138fbe34","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:37:45] staging.DEBUG: Inside staging environment  
[2025-07-15 06:37:45] staging.DEBUG: token: gauntlet  
[2025-07-15 06:37:45] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:37:45] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:38:17] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2VJKQoAc42Xce0zwHbPYr","idempotency_key":"payment_intent_351c169faf24a254a91e6a6332390dc0","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:38:17] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_351c169faf24a254a91e6a6332390dc0","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:38:26] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_351c169faf24a254a91e6a6332390dc0","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:38:27] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_351c169faf24a254a91e6a6332390dc0","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:38:27] staging.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl2VJKQoAc42Xce0zwHbPYr","error_code":"payment_intent_unexpected_state","error_message":"A processing error occurred.","donation_type":"user_donation"} 
[2025-07-15 06:38:27] staging.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl2VJKQoAc42Xce0zwHbPYr","error_code":"payment_intent_unexpected_state","error_message":"A processing error occurred.","donation_type":"user_donation"} 
[2025-07-15 06:38:56] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2VwKQoAc42Xce1oh4CD0S","idempotency_key":"payment_intent_a0d9155c9f054817e789f2005ff77c11","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:38:57] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_a0d9155c9f054817e789f2005ff77c11","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:40:52] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2XoKQoAc42Xce10wZT54y","idempotency_key":"payment_intent_59ef59a432e8d77948e306e77efbcf27","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:40:52] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_59ef59a432e8d77948e306e77efbcf27","email":"<EMAIL>","amount":"25"} 
